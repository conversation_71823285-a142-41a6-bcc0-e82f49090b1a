import {
  <PERSON>ton,
  Dialog,
  DialogBody,
  DialogFooter,
  FormGroup,
  InputGroup,
  Radio,
  Tab,
  Tabs,
} from "@blueprintjs/core";
import { addDoc, collection, onSnapshot } from "firebase/firestore";
import { useEffect, useState } from "react";
import { db } from "../../services/firebase";
import dayjs from "dayjs";

export default function NtrnlWorkforces() {
  const [workforces, setWorkforces] = useState([]);
  const [open, setOpen] = useState(false);
  const [cities, setCities] = useState([]);
  const [query, setQuery] = useState("");
  useEffect(() => {
    const unsubscribe = onSnapshot(
      collection(db, "workforces"),
      (snapOnWorkforces) => {
        setWorkforces(
          snapOnWorkforces.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          }))
        );
      }
    );
    return () => unsubscribe();
  }, []);
  return (
    <>
      <Dialog
        isOpen={open}
        onClose={() => setOpen(false)}
        title="Add Workforce"
      >
        <form
          onSubmit={(event) => {
            event.preventDefault();
            addDoc(collection(db, "workforces"), {
              code: event.target.code.value,
              location: JSON.parse(event.target.location.value),
              job: event.target.job.value,
              currentState: "Unknown",
              state: "Active",
              createdAt: dayjs().toDate(),
              updatedAt: dayjs().toDate(),
            });
          }}
        >
          <DialogBody>
            <FormGroup label="Code">
              <InputGroup name="code" type="text" required />
            </FormGroup>
            <FormGroup label="Location">
              <InputGroup
                onChange={(event) => setQuery(event.target.value)}
                rightElement={
                  <Button
                    icon="search"
                    onClick={() => {
                      fetch(
                        `https://geocoding-api.open-meteo.com/v1/search?name=${query}&count=5`
                      )
                        .then((response) => response.json())
                        .then((data) => {
                          setCities(data.results);
                        });
                    }}
                  />
                }
              />
              {cities?.map((city) => (
                <Radio
                  required
                  name="location"
                  key={city.id}
                  label={`${city.name}, ${city.admin1} ,${city.country}`}
                  value={JSON.stringify({
                    city: city.name,
                    country: city.country,
                    latitude: city.latitude,
                    longitude: city.longitude,
                  })}
                />
              ))}
            </FormGroup>
            <FormGroup label="Job">
              <InputGroup name="job" type="text" required />
            </FormGroup>
          </DialogBody>
          <DialogFooter
            actions={[
              <Button key="cancel" text="Cancel" />,
              <Button
                key="submit"
                text="Submit"
                type="submit"
                intent="primary"
              />,
            ]}
          />
        </form>
      </Dialog>
      <div
        style={{
          paddingInline: "50px 25px",
          paddingTop: 16,
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        <div style={{ fontSize: 24, fontWeight: 700 }}>Workforces</div>
        <div>
          <Button
            onClick={() => setOpen(true)}
            icon="add"
            text="Add Workforce"
            intent="primary"
          />
        </div>
      </div>
      <div
        style={{
          flex: 1,
          overflow: "auto",
          paddingInline: "50px 25px",
          paddingBlock: 16,
        }}
      >
        <div className="ntrnlUsersTable" style={{ paddingTop: 16 }}>
          <table>
            <thead>
              <tr>
                <th>Code</th>
                <th>Job</th>
                <th>City</th>
                <th>Country</th>
                <th>Mobility</th>
                <th>Current state</th>
              </tr>
            </thead>
            <tbody>
              {workforces.map((workforce) => (
                <tr key={workforce.id}>
                  <td>{workforce.code}</td>
                  <td>{workforce.job}</td>
                  <td>{workforce.location.city}</td>
                  <td>{workforce.location.country}</td>
                  <td>{workforce.mobility}</td>
                  <td>{workforce.currentState}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
}
