import { Colors, Icon } from "@blueprintjs/core";

export default function GlblLogo({
  logoColor = Colors.BLUE3,
  fontColor = Colors.DARK_GRAY3,
}) {
  return (
    <>
      <div
        onClick={() => window.location.assign("/")}
        style={{
          display: "flex",
          alignItems: "center",
          gap: 10,
          userSelect: "none",
          cursor: "pointer",
        }}
      >
        <Icon icon="cube" color={logoColor} size={20} />
        <div style={{ color: fontColor, fontWeight: 600 }}>FMAIP</div>
      </div>
    </>
  );
}
