import { Colors } from "@blueprintjs/core";
import { collection, onSnapshot } from "firebase/firestore";
import { useEffect, useState } from "react";
import Map, { <PERSON><PERSON>, <PERSON>er, Source } from "react-map-gl/mapbox";
import { db } from "../../services/firebase";

export default function NtrnlMap() {
  const [workforces, setWorkforces] = useState([]);
  const [sites, setSites] = useState([]);
  useEffect(() => {
    const unsubscribe = onSnapshot(
      collection(db, "workforces"),
      (snapOnWorkforces) => {
        setWorkforces(
          snapOnWorkforces.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          }))
        );
      }
    );
    return () => unsubscribe();
  }, []);
  return (
    <div style={{ flex: 1, borderTop: `1px solid ${Colors.LIGHT_GRAY1}` }}>
      <Map
        mapboxAccessToken={import.meta.env.VITE_TOKEN_MAPBOX}
        mapStyle={"mapbox://styles/mapbox/light-v10"}
      >
        {workforces.length < 0 &&
          workforces.map((workforce) => (
            <Marker
              key={workforce.id}
              latitude={workforce.location.latitude}
              longitude={workforce.location.longitude}
            />
          ))}
        {/CLUSTERS/}
        <Source
          id="workforces"
          type="geojson"
          data={{
            type: "FeatureCollection",
            features: workforces.map((workforce) => ({
              type: "Feature",
              geometry: {
                type: "Point",
                coordinates: [
                  workforce.location.longitude,
                  workforce.location.latitude,
                ],
              },
              properties: {
                id: workforce.id,
              },
            })),
          }}
        />
        <Layer
          id="workforces"
          type="circle"
          source="workforces"
          paint={{
            "circle-color": "red",
            "circle-radius": 10,
          }}
        />
        {sites.length < 0 &&
          sites.map((site) => (
            <Marker
              key={site.id}
              latitude={site.location.latitude}
              longitude={site.location.longitude}
            />
          ))}
      </Map>
    </div>
  );
}
