import {
  Button,
  Colors,
  EntityTitle,
  InputGroup,
  Menu,
  MenuDivider,
  MenuItem,
  Popover,
} from "@blueprintjs/core";
import GlblLogo from "../global/glbl-logo";
import { Outlet } from "react-router-dom";
import { auth } from "../../services/firebase";

export default function CmmnLayout() {
  return (
    <>
      <div style={{ minHeight: "100svh", display: "flex", flexFlow: "column" }}>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <div
            style={{
              width: 240,
              height: 60,
              display: "flex",
              alignItems: "center",
              borderRight: `1px solid ${Colors.LIGHT_GRAY1}`,
              paddingInline: 25,
            }}
          >
            <GlblLogo />
          </div>
          <div
            style={{
              paddingInline: "50px 25px",
              flex: 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <div>
              <InputGroup
                leftIcon="search"
                style={{
                  boxShadow: "none",
                  backgroundColor: Colors.LIGHT_GRAY4,
                }}
              />
            </div>
            <div>
              <Button icon="notifications" variant="minimal" />
              <Popover
                placement="bottom-end"
                content={
                  <Menu style={{ width: 240 }}>
                    <MenuDivider
                      title={
                        <EntityTitle
                          title={auth.currentUser.displayName}
                          subtitle={auth.currentUser.email}
                        />
                      }
                    />
                    <MenuDivider />
                    <MenuItem text="Account Settings" icon="user" />
                    <MenuItem text="Documentation" icon="book" />
                    <MenuItem text="Updates" icon="refresh" />
                    <MenuItem text="Support" icon="info-sign" />
                    <MenuDivider />
                    <MenuItem
                      intent="danger"
                      text="Sign Out"
                      icon="log-out"
                      onClick={() => auth.signOut()}
                    />
                  </Menu>
                }
              >
                <Button icon="user" variant="minimal" />
              </Popover>
            </div>
          </div>
        </div>
        <div style={{ flex: 1, overflow: "auto", display: "flex" }}>
          <Outlet />
        </div>
      </div>
    </>
  );
}
