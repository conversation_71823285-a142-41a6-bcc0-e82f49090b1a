import { Button, Tab, Tabs } from "@blueprintjs/core";
import { collection, onSnapshot } from "firebase/firestore";
import { useEffect, useState } from "react";
import { db } from "../../services/firebase";

export default function NtrnlUsers() {
  const [users, setUsers] = useState([]);
  useEffect(() => {
    const unsubscribe = onSnapshot(collection(db, "users"), (snapOnUsers) => {
      setUsers(
        snapOnUsers.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }))
      );
    });
    return () => unsubscribe();
  }, []);
  return (
    <>
      <div
        style={{
          paddingInline: "50px 25px",
          paddingTop: 16,
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        <div style={{ fontSize: 24, fontWeight: 700 }}>Users</div>
        <div>
          <Button icon="add" text="Add User" intent="primary" />
        </div>
      </div>
      <div
        style={{
          flex: 1,
          overflow: "auto",
          paddingInline: "50px 25px",
          paddingBlock: 16,
        }}
      >
        <div className="ntrnlUsersTable" style={{ paddingTop: 16 }}>
          <table>
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Role</th>
                <th>Type</th>
              </tr>
            </thead>
            <tbody>
              {users.map((user) => (
                <tr key={user.id}>
                  <td>{user.displayName}</td>
                  <td>{user.email}</td>
                  <td>{user.role}</td>
                  <td>{user.type}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
}
