import { useContext } from "react";
import { Navigate, Route, Routes } from "react-router-dom";
import { globalContext } from "./glbl-context";
import GlblSignin from "./pages/global/glbl-signin";
import GlblReset from "./pages/global/glbl-reset";
import ClntLayout from "./components/client/clnt-layout";
import CmmnLayout from "./components/common/cmmn-layout";
import ClntDashboard from "./pages/client/clnt-dashboard";
import NtrnlLayout from "./components/internal/ntrnl-layout";
import NtrnlDashboard from "./pages/internal/ntrnl-dashboard";
import NtrnlUsers from "./pages/internal/ntrnl-users";
import NtrnlMap from "./pages/internal/ntrnl-map";
import NtrnlWorkforces from "./pages/internal/ntrnl-workforces";

function App() {
  const { loged } = useContext(globalContext);
  return (
    <Routes>
      <Route path="*" element={<Navigate to={"/"} />} />
      <Route>
        {loged ? (
          <Route>
            <Route element={<CmmnLayout />}>
              <Route element={<ClntLayout />}>
                <Route path="" element={<ClntDashboard />} />
                <Route path="map" element={"Map View"} />
                <Route path="sites" element={"Sites"} />
                <Route path="planning" element={"Sites"} />
                <Route path="activities" element={"Activities"} />
              </Route>
              <Route path="internal" element={<NtrnlLayout />}>
                <Route path="" element={<NtrnlDashboard />} />
                <Route path="users" element={<NtrnlUsers />} />
                <Route path="map" element={<NtrnlMap />} />
                <Route path="planning" element={"Planning"} />
                <Route path="workforce" element={<NtrnlWorkforces />} />
              </Route>
            </Route>
            <Route path="doc">
              <Route path="" element={"Documentation"} />
            </Route>
          </Route>
        ) : (
          <Route>
            <Route path="" element={"App"} />
            <Route path="reset" element={<GlblReset />} />
            <Route path="auth" element={"Auth"} />
            <Route path="legal" element={"Legal"} />
            <Route path="about" element={"About"} />
            <Route path="contact" element={"Contact"} />
            <Route path="signup" element={"Sign Up"} />
            <Route path="signin" element={<GlblSignin />} />
          </Route>
        )}
      </Route>
    </Routes>
  );
}

export default App;
