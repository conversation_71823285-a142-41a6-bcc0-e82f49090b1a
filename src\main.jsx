import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from "react-router-dom";
import GlobalContext from "./glbl-context.jsx";

import App from "./App.jsx";

import "normalize.css";
import "@blueprintjs/core/lib/css/blueprint.css";
import "@blueprintjs/icons/lib/css/blueprint-icons.css";
import "mapbox-gl/dist/mapbox-gl.css";
import "./styles.css";
import { FocusStyleManager } from "@blueprintjs/core";

FocusStyleManager.onlyShowFocusOnTabs();

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <GlobalContext>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </GlobalContext>
  </StrictMode>
);
