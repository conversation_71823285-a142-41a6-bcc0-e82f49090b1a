import {
  sendPasswordResetEmail,
  signInWithEmailAndPassword,
} from "firebase/auth";
import { auth, db } from "./firebase";
import { OverlayToaster } from "@blueprintjs/core";
import { addDoc, collection } from "firebase/firestore";
import dayjs from "dayjs";
import CryptoJS from "crypto-js";

export const functions = {
  users: {
    auth: {
      signin: async (event) => {
        event.preventDefault();

        const toaster = await OverlayToaster.create();

        signInWithEmailAndPassword(
          auth,
          event.target.email.value,
          event.target.password.value
        )
          .then((user) => {
            addDoc(collection(db, "users", user.user.uid, "logs"), {
              type: "auth",
              title: "User login",
              description: `Login to your account on ${dayjs().format(
                "DD/MM/YYYY at HH:mm:ss"
              )} from ${navigator.userAgent}`,
              createdAt: dayjs().toDate(),
            });
          })
          .catch((error) => {
            toaster.show({
              intent: "warning",
              message: error.code,
            });
          });
      },
      reset: async (event) => {
        event.preventDefault();

        const toaster = await OverlayToaster.create();

        sendPasswordResetEmail(auth, event.target.email.value)
          .then(() => {
            toaster.show({
              intent: "success",
              message: "Password reset email sent",
            });
          })
          .catch((error) => {
            toaster.show({
              intent: "warning",
              message: error.code,
            });
          });
      },
    },
  },
  crypt: {
    encrypt: (data) => {
      const SECRET_KEY = import.meta.env.VITE_CRYPT_SECRET_KEY;
      try {
        const ciphertext = CryptoJS.AES.encrypt(data, SECRET_KEY).toString();
        return ciphertext;
      } catch (error) {
        console.error("Erreur lors du cryptage :", error);
        throw new Error("Cryptage échoué");
      }
    },
    decrypt: (encryptedData) => {
      const SECRET_KEY = import.meta.env.VITE_CRYPT_SECRET_KEY;
      try {
        const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
        const originalText = bytes.toString(CryptoJS.enc.Utf8);
        if (!originalText) throw new Error("Clé invalide ou donnée corrompue");
        return originalText;
      } catch (error) {
        console.error("Erreur lors du décryptage :", error);
        throw new Error("Décryptage échoué");
      }
    },
  },
};
