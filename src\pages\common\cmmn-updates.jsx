import { Outlet } from "react-router-dom";
import Glbl<PERSON>ogo from "../../components/global/glbl-logo";
import { Button, Colors, CompoundTag } from "@blueprintjs/core";

export default function CmmnUpdates() {
  return (
    <>
      <div style={{ display: "flex", minHeight: "100svh", flexFlow: "column" }}>
        <div
          style={{
            padding: 15,
            borderBottom: `1px solid ${Colors.LIGHT_GRAY1}`,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <div>
            <GlblLogo />
          </div>
          <div>
            <CompoundTag minimal intent="primary" leftContent="V">
              0.0.1
            </CompoundTag>
          </div>
          <div>
            <Button
              icon="cross"
              variant="minimal"
              onClick={() => window.close()}
            />
          </div>
        </div>
        <div
          style={{
            flex: 1,
            overflow: "auto",
            display: "flex",
            backgroundColor: Colors.LIGHT_GRAY5,
          }}
        >
          <Outlet />
        </div>
      </div>
    </>
  );
}
