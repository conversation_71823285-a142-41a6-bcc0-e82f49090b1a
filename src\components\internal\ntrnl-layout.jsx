import { Colors, Menu, <PERSON>u<PERSON><PERSON><PERSON>, MenuItem } from "@blueprintjs/core";
import { Outlet, useNavigate } from "react-router-dom";

export default function NtrnlLayout() {
  const navigate = useNavigate();
  return (
    <>
      <div
        style={{
          width: 240,
          borderRight: `1px solid ${Colors.LIGHT_GRAY1}`,
          paddingInline: 13,
          paddingTop: 16,
        }}
      >
        <Menu style={{}}>
          <MenuItem
            text="Dashboard"
            icon="dashboard"
            intent={
              window.location.pathname === "/internal" ? "primary" : "none"
            }
            onClick={() => navigate("")}
          />
          <MenuItem
            text="Map View"
            icon="map"
            intent={
              window.location.pathname === "/internal/map" ? "primary" : "none"
            }
            onClick={() => navigate("map")}
          />
          <MenuItem
            text="Users"
            icon="user"
            intent={
              window.location.pathname === "/internal/users"
                ? "primary"
                : "none"
            }
            onClick={() => navigate("users")}
          />
          <MenuItem
            text="Planning"
            icon="calendar"
            intent={
              window.location.pathname === "/internal/planning"
                ? "primary"
                : "none"
            }
            onClick={() => navigate("planning")}
          />
          <MenuItem
            text="Workforce"
            icon="people"
            intent={
              window.location.pathname === "/internal/workforce"
                ? "primary"
                : "none"
            }
            onClick={() => navigate("workforce")}
          />
        </Menu>
      </div>
      <div
        style={{
          flex: 1,
          overflow: "auto",
          display: "flex",
          flexFlow: "column",
        }}
      >
        <Outlet />
      </div>
    </>
  );
}
