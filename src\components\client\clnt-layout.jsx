import { Colors, Menu, MenuItem } from "@blueprintjs/core";
import { useEffect, useState } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import { auth, db } from "../../services/firebase";
import { collection, onSnapshot, query, where } from "firebase/firestore";

export default function ClntLayout() {
  const navigate = useNavigate();
  const [sites, setSites] = useState([]);

  useEffect(() => {
    const unsubscribe = onSnapshot(
      query(
        collection(db, "sites"),
        where("members", "array-contains", auth.currentUser.uid)
      ),
      (snapOnSites) => {
        setSites(
          snapOnSites.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          }))
        );
      }
    );

    return () => unsubscribe();
  }, []);
  return (
    <>
      <div
        style={{
          width: 240,
          borderRight: `1px solid ${Colors.LIGHT_GRAY1}`,
          paddingInline: 13,
          paddingTop: 16,
        }}
      >
        <Menu>
          <MenuItem
            text="Dashboard"
            icon="dashboard"
            intent={window.location.pathname === "/" ? "primary" : "none"}
            onClick={() => navigate("")}
          />
          <MenuItem
            text="Map View"
            icon="map"
            onClick={() => navigate("map")}
            intent={window.location.pathname === "/map" ? "primary" : "none"}
          />
        </Menu>
      </div>
      <div style={{ flex: 1, overflow: "auto" }}>
        <Outlet context={{ sites }} />
      </div>
    </>
  );
}
