import { Button, Colors, FormGroup, InputGroup } from "@blueprintjs/core";
import Glbl<PERSON>ogo from "../../components/global/glbl-logo";
import { functions } from "../../services/functions";

export default function GlblSignin() {
  return (
    <>
      <div
        style={{
          display: "flex",
          minHeight: "100svh",
          flexFlow: "column",
          backgroundColor: Colors.LIGHT_GRAY5,
        }}
      >
        <div style={{ padding: 15 }}>
          <GlblLogo />
        </div>
        <div
          style={{
            flex: 1,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <div style={{ width: 360, padding: 30 }}>
            <div style={{ marginBottom: 30 }}>
              <div style={{ fontWeight: 600, fontSize: 20 }}>Sign In</div>
              <div>Add your creditential to sign in.</div>
            </div>
            <form
              onSubmit={(event) => {
                event.preventDefault();
                functions.users.auth.signin(event);
              }}
            >
              <FormGroup label="Email Address">
                <InputGroup name="email" type="email" required />
              </FormGroup>
              <FormGroup
                label="Password"
                helperText={
                  <div style={{ textAlign: "right" }}>
                    <span
                      style={{ cursor: "pointer" }}
                      onClick={() => window.open("/reset")}
                    >
                      Forgot your password?
                    </span>
                  </div>
                }
              >
                <InputGroup name="password" type="password" required />
              </FormGroup>
              <Button
                style={{ marginTop: 30 }}
                text="Sign In"
                fill
                intent="primary"
                type="submit"
              />
            </form>
          </div>
        </div>
      </div>
    </>
  );
}
