import { Button, Colors, FormGroup, InputGroup } from "@blueprintjs/core";
import { functions } from "../../services/functions";

export default function GlblReset() {
  return (
    <>
      <div
        style={{
          display: "flex",
          minHeight: "100svh",
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: Colors.LIGHT_GRAY5,
        }}
      >
        <div style={{ width: 360, padding: 30 }}>
          <div style={{ marginBottom: 30 }}>
            <div style={{ fontWeight: 600, fontSize: 20 }}>Reset Password</div>
            <div>
              Enter your email address and we will send you a link to reset your
              password.
            </div>
          </div>
          <form
            onSubmit={(event) => {
              functions.users.auth.reset(event);
            }}
          >
            <FormGroup label="Email Address">
              <InputGroup name="email" type="email" required />
            </FormGroup>
            <Button
              style={{ marginTop: 30 }}
              type="submit"
              text="Reset"
              fill
              intent="primary"
            />
          </form>
        </div>
      </div>
    </>
  );
}
